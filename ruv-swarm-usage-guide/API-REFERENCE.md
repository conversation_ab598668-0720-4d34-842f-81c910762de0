# ruv-swarm API Reference

Complete reference for all ruv-swarm MCP tools, parameters, and valid values.

## 🎯 Quick Reference Table

| Tool | Purpose | Key Parameters |
|------|---------|----------------|
| `swarm_init` | Initialize swarm topology | topology, maxAgents, strategy |
| `agent_spawn` | Create agent | type, name, capabilities |
| `daa_init` | Initialize DAA service | enableLearning, enableCoordination |
| `task_orchestrate` | Execute task | task, strategy, priority |
| `neural_train` | Train agents | agentId, iterations |

## 📚 Complete API Documentation

### mcp__ruv-swarm__swarm_init

Initializes a swarm with specified topology.

**Parameters:**
- `topology` (required): `"mesh"` | `"hierarchical"` | `"ring"` | `"star"`
  - **mesh**: All agents connected to all (best for exploration)
  - **hierarchical**: Tree structure (best for large teams)
  - **star**: Central coordinator (best for simple tasks)
  - **ring**: Circular pattern (best for sequential processing)
- `maxAgents` (optional): Number 1-100, default: 5
- `strategy` (optional): `"balanced"` | `"specialized"` | `"adaptive"`

**Example:**
```javascript
mcp__ruv-swarm__swarm_init { 
  topology: "hierarchical", 
  maxAgents: 30, 
  strategy: "specialized" 
}
```

### mcp__ruv-swarm__agent_spawn

Creates a new agent in the swarm.

**Parameters:**
- `type` (required): `"researcher"` | `"coder"` | `"analyst"` | `"optimizer"` | `"coordinator"` | `"tester"` | `"reviewer"` | `"documenter"`
- `name` (optional): Custom agent name
- `capabilities` (optional): Array of capability strings

**Valid Agent Types:**
- **researcher**: Information gathering, pattern recognition
- **coder**: Code generation and implementation
- **analyst**: Data analysis and evaluation
- **optimizer**: Performance and efficiency optimization
- **coordinator**: Task coordination and management
- **tester**: Testing and validation
- **reviewer**: Code review and quality assurance
- **documenter**: Documentation creation

**Example:**
```javascript
mcp__ruv-swarm__agent_spawn { 
  type: "coordinator", 
  name: "Chief Architect",
  capabilities: ["system_design", "team_coordination"]
}
```

### mcp__ruv-swarm__daa_init

Initializes Decentralized Autonomous Agents service.

**Parameters:**
- `enableLearning` (optional): Boolean, enables neural learning
- `enableCoordination` (optional): Boolean, enables peer coordination
- `persistenceMode` (optional): `"auto"` | `"memory"` | `"disk"`

**Example:**
```javascript
mcp__ruv-swarm__daa_init { 
  enableLearning: true, 
  enableCoordination: true,
  persistenceMode: "auto"
}
```

### mcp__ruv-swarm__daa_agent_create

Creates an autonomous agent with DAA capabilities.

**Parameters:**
- `id` (required): Unique agent identifier
- `cognitivePattern` (optional): `"convergent"` | `"divergent"` | `"lateral"` | `"systems"` | `"critical"` | `"adaptive"`
- `enableMemory` (optional): Boolean
- `learningRate` (optional): Number 0-1
- `capabilities` (optional): Array of strings

**Cognitive Patterns:**
- **convergent**: Focus on finding single best solution
- **divergent**: Explore multiple possibilities
- **lateral**: Creative, out-of-box thinking
- **systems**: Holistic, interconnected approach
- **critical**: Analytical evaluation
- **adaptive**: Learning and adjusting

**Example:**
```javascript
mcp__ruv-swarm__daa_agent_create {
  id: "neural-coordinator",
  cognitivePattern: "adaptive",
  enableMemory: true,
  learningRate: 0.01,
  capabilities: ["coordination", "task_distribution"]
}
```

### mcp__ruv-swarm__task_orchestrate

Orchestrates a task across the swarm.

**Parameters:**
- `task` (required): Task description
- `strategy` (optional): `"parallel"` | `"sequential"` | `"adaptive"`
- `priority` (optional): `"low"` | `"medium"` | `"high"` | `"critical"`
- `maxAgents` (optional): Number 1-10

**Example:**
```javascript
mcp__ruv-swarm__task_orchestrate { 
  task: "Implement authentication system",
  strategy: "parallel",
  priority: "high"
}
```

### mcp__ruv-swarm__neural_train

Trains neural agents with sample tasks.

**Parameters:**
- `agentId` (optional): Specific agent ID to train
- `iterations` (optional): Number 1-100, default: 10

**Example:**
```javascript
mcp__ruv-swarm__neural_train { 
  agentId: "neural-coordinator",
  iterations: 20 
}
```

### mcp__ruv-swarm__memory_usage

Manages swarm memory operations.

**Parameters:**
- `action` (optional): `"store"` | `"retrieve"` | `"list"` | `"clear"`
- `key` (optional): Memory key string
- `value` (optional): Value to store (for store action)
- `pattern` (optional): Pattern for list/clear actions
- `detail` (optional): `"summary"` | `"detailed"` | `"by-agent"`

**Example:**
```javascript
mcp__ruv-swarm__memory_usage { 
  action: "store",
  key: "project/architecture",
  value: { design: "microservices", version: "1.0" }
}
```

### mcp__ruv-swarm__swarm_status

Gets current swarm status.

**Parameters:**
- `verbose` (optional): Boolean, include detailed agent info

**Example:**
```javascript
mcp__ruv-swarm__swarm_status { verbose: true }
```

### mcp__ruv-swarm__agent_list

Lists active agents in swarm.

**Parameters:**
- `filter` (optional): `"all"` | `"active"` | `"idle"` | `"busy"`

### mcp__ruv-swarm__agent_metrics

Gets performance metrics for agents.

**Parameters:**
- `agentId` (optional): Specific agent ID
- `metric` (optional): `"all"` | `"cpu"` | `"memory"` | `"tasks"` | `"performance"`

### mcp__ruv-swarm__task_status

Checks progress of running tasks.

**Parameters:**
- `taskId` (optional): Specific task ID
- `detailed` (optional): Boolean

### mcp__ruv-swarm__benchmark_run

Executes performance benchmarks.

**Parameters:**
- `type` (optional): `"all"` | `"wasm"` | `"swarm"` | `"agent"` | `"task"`
- `iterations` (optional): Number 1-100

### mcp__ruv-swarm__daa_workflow_create

Creates autonomous workflow.

**Parameters:**
- `id` (required): Workflow ID
- `name` (required): Workflow name
- `strategy` (optional): `"parallel"` | `"sequential"` | `"adaptive"`
- `steps` (optional): Array of step objects
- `dependencies` (optional): Object mapping step dependencies

### mcp__ruv-swarm__daa_workflow_execute

Executes DAA workflow.

**Parameters:**
- `workflow_id` (required): Workflow to execute
- `parallelExecution` (optional): Boolean
- `agentIds` (optional): Array of agent IDs

## 🔧 Hook Configuration

Configure in `.claude/settings.json`:

```json
{
  "hooks": {
    "pre_edit": "npx ruv-swarm hook pre-task --description \"${file}\"",
    "post_edit": "npx ruv-swarm hook post-edit --file \"${file}\" --train-neural true",
    "pre_commit": "npx ruv-swarm hook notification --message \"Preparing commit\"",
    "post_commit": "npx ruv-swarm hook session-end --export-metrics true"
  }
}
```

## ⚡ BatchTool Pattern

**MANDATORY**: Always batch multiple operations in a single message:

```javascript
[BatchTool]:
  mcp__ruv-swarm__daa_init { enableLearning: true }
  mcp__ruv-swarm__swarm_init { topology: "mesh" }
  mcp__ruv-swarm__agent_spawn { type: "researcher" }
  mcp__ruv-swarm__agent_spawn { type: "coder" }
  mcp__ruv-swarm__task_orchestrate { task: "Build API" }
```

## 🔴 Common Errors

| Error | Cause | Solution |
|-------|-------|----------|
| "Invalid agent type" | Using non-existent type like "architect" | Use valid types from list above |
| "Cannot read property" | DAA not initialized | Initialize DAA before swarm |
| "MCP server not found" | Server not running | Run `claude mcp add ruv-swarm` |

## 📊 Initialization Order

**CRITICAL**: Always initialize in this order:
1. `daa_init` - Initialize DAA service
2. `swarm_init` - Create swarm topology
3. `agent_spawn` or `daa_agent_create` - Create agents
4. `task_orchestrate` - Execute tasks

## 🎯 Quick Command Reference

### Basic Commands
```bash
# Diagnose issues
npx ruv-swarm diagnose --verbose

# Test hooks
npx ruv-swarm hook pre-task --description "test" --debug true

# Check MCP status
claude mcp list | grep ruv-swarm
```

### Advanced CLI Operations
```bash
# Memory management
npx ruv-swarm memory export --output ./memory-backup.json
npx ruv-swarm memory import --input ./memory-backup.json

# Performance analysis
npx ruv-swarm performance analyze --task-id "recent"
npx ruv-swarm performance suggest

# Trace operations
npx ruv-swarm trace start --output trace.log
npx ruv-swarm trace stop
npx ruv-swarm trace analyze trace.log

# Neural model management
npx ruv-swarm neural export --model all --output ./neural-weights.json
npx ruv-swarm neural import --input ./neural-weights.json
```

### Hook Parameters Reference
- `--auto-spawn-agents false` - Disable automatic agent spawning
- `--memory-key "pattern"` - Specify memory storage pattern
- `--telemetry true` - Enable telemetry collection
- `--debug true` - Enable debug output
- `--train-neural true` - Trigger neural training