#!/bin/bash
# Shadow Workforce Setup Script for <PERSON>

echo "🚀 Setting up Mister <PERSON> Shadow Workforce..."

# Check prerequisites
command -v node >/dev/null 2>&1 || { 
  echo "❌ Node.js is required but not installed."; 
  exit 1; 
}

command -v claude >/dev/null 2>&1 || { 
  echo "❌ Claude CLI is required but not installed."; 
  exit 1; 
}

# Install dependencies
echo "📦 Installing dependencies..."
cd "$(dirname "$0")/.."
npm init -y
npm install events child_process fs path

# Verify MCP server
echo "🔌 Checking ruv-swarm MCP server..."
if ! claude mcp list | grep -q "ruv-swarm"; then
  echo "⚙️ Adding ruv-swarm MCP server..."
  claude mcp add ruv-swarm npx ruv-swarm mcp start
fi

# Create state directories
echo "📁 Creating state directories..."
mkdir -p state/{agents,memory,checkpoints,workflows}
mkdir -p ../daa-neural-network/state/{neural,training}

# Link neural network modules
echo "🔗 Linking neural network modules..."
if [ -d "../daa-neural-network/src" ]; then
  ln -sf ../daa-neural-network/src ./neural-src
  echo "✅ Neural network modules linked"
else
  echo "⚠️ Neural network source not found, skipping link"
fi

# Create package.json with scripts
cat > package.json << 'EOF'
{
  "name": "mister-smith-shadow-workforce",
  "version": "1.0.0",
  "description": "Shadow workforce for Mister <PERSON> development",
  "main": "index.js",
  "scripts": {
    "start": "node scripts/start-workforce.js",
    "demo": "node ../daa-neural-network/scripts/demo.js",
    "monitor": "node scripts/monitor.js",
    "test": "node test/smoke-test.js && node test/integration-test.js"
  },
  "dependencies": {
    "events": "^3.3.0",
    "child_process": "^1.0.2"
  }
}
EOF

echo "✅ Mister Smith Shadow Workforce setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Run 'npm start' to start the workforce"
echo "2. Run 'npm run demo' to see a demonstration"
echo "3. Run 'npm run monitor' to monitor swarm activity"
echo "4. Run 'npm test' to verify installation"