/**
 * Hook Manager for ruv-swarm integration
 */

const { spawn } = require('child_process');

class HookManager {
  static async execute(hookType, params) {
    const args = Object.entries(params)
      .map(([k, v]) => `--${k}="${v}"`)
      .join(' ');
    
    return new Promise((resolve) => {
      spawn(`npx ruv-swarm hook ${hookType} ${args}`, { shell: true })
        .on('close', resolve);
    });
  }
  
  static async preTask(description) {
    return this.execute('pre-task', { description, 'auto-spawn-agents': false });
  }
  
  static async postEdit(file) {
    return this.execute('post-edit', { file, 'memory-key': `swarm/files/${file}` });
  }
  
  static async notification(message) {
    return this.execute('notification', { message, telemetry: true });
  }
}

module.exports = HookManager;