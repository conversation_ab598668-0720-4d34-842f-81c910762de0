/**
 * DAA Workflow Definitions for Mister Smith Framework
 */

const workflows = {
  "architecture-review": {
    id: "architecture-review",
    name: "Architecture Review & Planning",
    steps: [
      { id: "analyze-requirements", type: "analysis", hooks: ["pre-task", "notification"] },
      { id: "design-architecture", type: "design", hooks: ["pre-task", "post-edit", "notification"] },
      { id: "validate-design", type: "validation", hooks: ["notification"] }
    ],
    agentTypes: ["architect", "analyst", "reviewer"]
  },
  "core-implementation": {
    id: "core-implementation",
    name: "Core System Implementation",
    steps: [
      { id: "setup-project", type: "implementation", hooks: ["pre-task", "post-edit"] },
      { id: "implement-core-modules", type: "implementation", hooks: ["pre-task", "post-edit", "notification"] },
      { id: "implement-async-patterns", type: "implementation", hooks: ["pre-task", "post-edit"] }
    ],
    agentTypes: ["coder", "architect"]
  }
};

module.exports = { workflows };